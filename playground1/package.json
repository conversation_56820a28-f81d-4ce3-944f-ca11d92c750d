{"name": "@formily/element-plus-playground", "version": "1.0.0-beta.2", "description": "", "main": "src/index.ts", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --no-module"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@formily/vue": "2.2.1", "@formily/element-plus": "1.0.0-beta.2", "@formily/element-plus-prototypes": "1.0.0-beta.2", "@formily/element-plus-renderer": "1.0.0-beta.2", "@formily/element-plus-setters": "1.0.0-beta.2", "@formily/element-plus-settings-form": "1.0.0-beta.2", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "@vue/cli-plugin-babel": "~5.0.4", "@vue/cli-plugin-router": "~5.0.4", "@vue/cli-plugin-typescript": "~5.0.4", "@vue/cli-service": "~5.0.4", "@guolao/vue-monaco-editor": "^0.0.5", "sass": "^1.51.0", "sass-loader": "^12.6.0", "typescript": "~4.6.4", "vue": "^3.2.33", "vue-demi": "^0.12.5", "resize-observer-polyfill": "^1.5.1", "@designable/core": "1.0.0-beta.45", "@designable/formily-transformer": "1.0.0-beta.45", "@designable/shared": "1.0.0-beta.45", "@vue/babel-plugin-jsx": "*", "@formily/grid": "*", "vuedraggable": "^4.1.0", "vue-slicksort": "^2.0.0-alpha.5", "core-js": "^3.22.5", "element-plus": "^2.2.0", "webpack-dev-server": "^4.9.0", "webpack": "*", "webpack-cli": "*", "ts-node": "^10.7.0", "file-loader": "*", "less-loader": "*", "less": "*", "classnames": "*"}}